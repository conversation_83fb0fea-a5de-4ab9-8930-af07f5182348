package com.jly.sharding.adapter.algorithm;

import com.jly.sharding.adapter.chain.DatasourcePostprocessChain;
import com.jly.sharding.adapter.chain.ProcessChainManager;
import com.jly.sharding.adapter.context.ShardingDetermineContext;
import com.jly.sharding.adapter.dto.DatasourceShardingDetermines;
import com.jly.sharding.adapter.dto.ShardingDetermines;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.sharding.api.sharding.hint.HintShardingValue;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-14
 */
@Slf4j
public class DatasourceHintShardingAlgorithm extends AbstractHintShardingAlgorithm {

    /**
     * 从库负载均衡器
     */

    @Override
    public String getType() {
        return "HINT_FB_ACCESS_DATASOURCE";
    }

    /**
     * 从库名称映射缓存
     * Key: {融担编号}_slave (例如: 12345_slave)
     * Value: 该融担编号下所有从库的完整名称集合 (例如: [12345_slave_0, 12345_slave_1, 12345_slave_2])
     */
    public static Map<String, Set<String>> slaveNames = new HashMap<>();
    /***可用库名称*/
    public static Set<String> availableNames;

    @Override
    protected Collection<String> shardingDetermines(Set<String> availableTargetNames, ShardingDetermines determines, HintShardingValue<String> shardingValue) {
        if (slaveNames.isEmpty()) {
            this.buildSalveNames(availableTargetNames);
        }
        if (availableNames == null) {
            availableNames = availableTargetNames;
        }
        // 将算法获取到的值设置的pending中
        DatasourceShardingDetermines databaseDetermines = determines.getDatabaseDetermines();
        // 待处理值, 如果处理链条不处理则取默认值
        String invokeSimpleInfo = ShardingDetermineContext.getInvokeSimpleInfo();
        String pendingValue = shardingValue.getValues().iterator().next();
        if (pendingValue == null) {
            log.error("No datasource sharding value found for method: {}", invokeSimpleInfo);
            throw new RuntimeException("No datasource sharding value found for method: " + invokeSimpleInfo);
        }
        databaseDetermines.setPendingValue(pendingValue);
        databaseDetermines.setFinalName(pendingValue);
        // 执行后置处理器链条 扩展点
        ProcessChainManager.process(determines, DatasourcePostprocessChain.class);

        // 提取最终表名
        String finalDatasourceName = databaseDetermines.getFinalName();
        if (finalDatasourceName == null) {
            log.error("No final datasource name found for method: {}", invokeSimpleInfo);
            throw new RuntimeException("No final datasource name found for method: " + invokeSimpleInfo);
        }

        // 最终获取的库名和可用库名不匹配
        if (!availableTargetNames.contains(finalDatasourceName)) {
            log.error("No available datasource sharding value found for method: {}, finalName: {}, available: {}",
                    invokeSimpleInfo, finalDatasourceName, availableTargetNames);
            throw new RuntimeException("No available datasource sharding value found for method: " + invokeSimpleInfo);
        }

        log.info("HintShardingAlgorithm the actual database executed: {},{},{}", this.getType(), finalDatasourceName, invokeSimpleInfo);
        return Collections.singleton(finalDatasourceName);
    }

    /**
     * 构建从库名称映射
     * <p>
     * 将 availableTargetNames 中的从库按融担编号分组
     * 例如：
     * 输入: [12345_master, 12345_slave_0, 12345_slave_1, 67890_master, 67890_slave_0]
     * 输出: {
     * "12345_slave" -> [12345_slave_0, 12345_slave_1],
     * "67890_slave" -> [67890_slave_0]
     * }
     *
     * @param availableTargetNames 所有可用的数据源名称
     */
    private void buildSalveNames(Set<String> availableTargetNames) {
        log.info("开始构建从库名称映射，可用数据源数量: {}", availableTargetNames.size());

        for (String availableTargetName : availableTargetNames) {
            // 检查是否是从库格式：{融担编号}_slave_{索引}
            if (availableTargetName.contains("_slave_")) {
                try {
                    // 提取融担编号部分，例如从 "12345_slave_0" 提取 "12345"
                    int slaveIndex = availableTargetName.lastIndexOf("_slave_");
                    if (slaveIndex > 0) {
                        String fbAccessNo = availableTargetName.substring(0, slaveIndex);
                        String slaveGroupKey = fbAccessNo + "_slave";

                        // 将该从库添加到对应融担编号的集合中
                        slaveNames.computeIfAbsent(slaveGroupKey, k -> new HashSet<>())
                                .add(availableTargetName);

                        log.debug("添加从库映射: {} -> {}", slaveGroupKey, availableTargetName);
                    }
                } catch (Exception e) {
                    log.warn("解析从库名称失败: {}, 错误: {}", availableTargetName, e.getMessage());
                }
            }
        }

        log.info("从库名称映射构建完成，共 {} 个融担编号的从库组", slaveNames.size());
        if (log.isDebugEnabled()) {
            slaveNames.forEach((key, value) ->
                    log.debug("从库组 {} 包含 {} 个从库: {}", key, value.size(), value));
        }
    }
}
